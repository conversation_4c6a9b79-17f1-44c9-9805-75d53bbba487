package com.subfg.subfgapi.service;

import java.util.concurrent.TimeUnit;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.subfg.common.constans.RedisConstants;
import com.subfg.common.exception.BusinessException;
import com.subfg.common.util.RedisUtil;
import com.subfg.common.util.VerifyCodeUtil;
import com.subfg.domain.entity.user.User;
import com.subfg.domain.request.LoginReq;
import com.subfg.domain.request.SendEmailCodeReq;
import com.subfg.domain.vo.LoginVO;
import com.subfg.repository.mapper.UserMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class AuthService {

    private final UserMapper userMapper;
    private final RedisUtil redisUtil;
    private final EmailService emailService;


    /**
     * 发送邮箱验证码
     */
    public void sendEmailCode(SendEmailCodeReq req){
        // 1. 验证邮箱是否存在
        User user = userMapper.selectOne(new LambdaQueryWrapper<User>().eq(User::getEmail, req.getEmail()));
        if (user == null) {
            throw new BusinessException("user.not.exists");
        }

        // 2. 检查发送频率限制
        String redisKey = RedisConstants.EMAIL_CODE_KEY + req.getEmail();
        String existingCode = redisUtil.getString(redisKey);
        if (existingCode != null) {
            // 获取剩余过期时间
            Long expireTime = redisUtil.getExpire(redisKey, TimeUnit.SECONDS);
            if (expireTime != null && expireTime > (RedisConstants.EMAIL_CODE_EXPIRE_MINUTES * 60 - RedisConstants.EMAIL_CODE_SEND_INTERVAL_SECONDS)) {
                throw new BusinessException("email.code.send.too.frequent");
            }
        }

        // 3. 生成并存储验证码
        String code = VerifyCodeUtil.generateNumberCode(6);
        redisUtil.set(redisKey, code, RedisConstants.EMAIL_CODE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        log.debug("验证码已存储 - 邮箱: {}, 验证码: {}", req.getEmail(), code);

        // 4. 异步发送邮件
        try {
            emailService.sendVerifyCodeEmail(req.getEmail(), code, req.getType());
            log.info("验证码发送成功 - 邮箱: {}, 类型: {}", req.getEmail(), req.getType());
        } catch (Exception e) {
            log.error("验证码邮件发送失败 - 邮箱: {}", req.getEmail(), e);
            throw new BusinessException("email.send.failed");
        }
    }

    /**
     * 用户登录
     */
    public LoginVO login(LoginReq req){
        // 1. 验证邮箱是否存在
        User user = userMapper.selectOne(new LambdaQueryWrapper<User>().eq(User::getEmail, req.getEmail()));
        if (user == null) {
            throw new BusinessException("user.not.exists");
        }

        // 2. 验证密码是否正确
        if (!user.getPassword().equals(req.getPassword())) {
            throw new BusinessException("password.incorrect");
        }

        return null;
    }

}
