package com.subfg.common.exception;

/**
 * 参数验证异常类
 * 用于处理参数验证失败的情况
 */
public class ValidationException extends RuntimeException {

    private String field;
    private Object value;
    private String messageCode;
    private Object[] args;

    public ValidationException(String messageCode) {
        super(messageCode);
        this.messageCode = messageCode;
    }

    public ValidationException(String field, String messageCode) {
        super(messageCode);
        this.field = field;
        this.messageCode = messageCode;
    }

    public ValidationException(String field, Object value, String messageCode) {
        super(messageCode);
        this.field = field;
        this.value = value;
        this.messageCode = messageCode;
    }

    public ValidationException(String field, String messageCode, Object... args) {
        super(messageCode);
        this.field = field;
        this.messageCode = messageCode;
        this.args = args;
    }

    public ValidationException(String messageCode, Throwable cause) {
        super(messageCode, cause);
        this.messageCode = messageCode;
    }

    public String getField() {
        return field;
    }

    public Object getValue() {
        return value;
    }

    public String getMessageCode() {
        return messageCode;
    }

    public Object[] getArgs() {
        return args;
    }
}
