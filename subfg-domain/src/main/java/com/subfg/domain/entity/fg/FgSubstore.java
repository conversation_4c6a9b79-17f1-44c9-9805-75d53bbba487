package com.subfg.domain.entity.fg;

import java.io.Serializable;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 家庭组订阅信息实体类
 * 对应数据库表：fg_substore
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("fg_substore")
public class FgSubstore implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 家庭组ID（主键）
     */
    @TableId(value = "family_group_id", type = IdType.INPUT)
    private String familyGroupId;

    /**
     * 家庭组名称
     */
    @TableField("family_group_name")
    private String familyGroupName;

    /**
     * 地区ID
     */
    @TableField("region_id")
    private Integer regionId;

    /**
     * 产品ID
     */
    @TableField("product_id")
    private Integer productId;

    /**
     * 分类ID
     */
    @TableField("category_id")
    private String categoryId;

    /**
     * 创建用户
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 预订用户ID
     */
    @TableField("booking_user_id")
    private String bookingUserId;

    /**
     * 家庭组状态
     */
    @TableField("family_group_status")
    private Integer familyGroupStatus;

    /**
     * 家庭组类型
     */
    @TableField("family_group_type")
    private Integer familyGroupType;

    /**
     * 实际加入人数
     */
    @TableField("actual_join_count")
    private Integer actualJoinCount;

    /**
     * 加入人数
     */
    @TableField("join_count")
    private Integer joinCount;

    /**
     * 总空位数
     */
    @TableField("sum_vacancy")
    private Integer sumVacancy;

    /**
     * 套餐ID
     */
    @TableField("plan_id")
    private String planId;

    /**
     * 计费周期
     */
    @TableField("billing_cycle")
    private Integer billingCycle;

    /**
     * 金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 描述
     */
    @TableField("description")
    private String description;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Long updateTime;

    /**
     * 最新加入时间
     */
    @TableField("latest_join_time")
    private Long latestJoinTime;

    /**
     * 服务过期时间
     */
    @TableField("service_expire_time")
    private Long serviceExpireTime;

    /**
     * 审核图片
     */
    @TableField("review_picture")
    private String reviewPicture;

    /**
     * 第三方详情
     */
    @TableField("third_detail")
    private String thirdDetail;

    /**
     * 原因
     */
    @TableField("reason")
    private String reason;

    /**
     * 检查创建时间
     */
    @TableField("check_create_time")
    private Long checkCreateTime;

    /**
     * 检查时间
     */
    @TableField("check_time")
    private Long checkTime;

    /**
     * 检查用户
     */
    @TableField("check_user")
    private Long checkUser;

    /**
     * 检查次数
     */
    @TableField("check_count")
    private Integer checkCount;

    /**
     * 是否新产品
     */
    @TableField("is_new_product")
    private Integer isNewProduct;
}
