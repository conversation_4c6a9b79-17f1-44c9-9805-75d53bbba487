<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.subfg.repository.mapper.FgSubstoreMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.subfg.domain.entity.FgSubstore">
        <id column="family_group_id" property="familyGroupId" />
        <result column="family_group_name" property="familyGroupName" />
        <result column="region_id" property="regionId" />
        <result column="product_id" property="productId" />
        <result column="category_id" property="categoryId" />
        <result column="create_user" property="createUser" />
        <result column="booking_user_id" property="bookingUserId" />
        <result column="family_group_status" property="familyGroupStatus" />
        <result column="family_group_type" property="familyGroupType" />
        <result column="actual_join_count" property="actualJoinCount" />
        <result column="join_count" property="joinCount" />
        <result column="sum_vacancy" property="sumVacancy" />
        <result column="plan_id" property="planId" />
        <result column="billing_cycle" property="billingCycle" />
        <result column="amount" property="amount" />
        <result column="description" property="description" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="latest_join_time" property="latestJoinTime" />
        <result column="service_expire_time" property="serviceExpireTime" />
        <result column="review_picture" property="reviewPicture" />
        <result column="third_detail" property="thirdDetail" />
        <result column="reason" property="reason" />
        <result column="check_create_time" property="checkCreateTime" />
        <result column="check_time" property="checkTime" />
        <result column="check_user" property="checkUser" />
        <result column="check_count" property="checkCount" />
        <result column="is_new_product" property="isNewProduct" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        family_group_id, family_group_name, region_id, product_id, category_id, 
        create_user, booking_user_id, family_group_status, family_group_type, 
        actual_join_count, join_count, sum_vacancy, plan_id, billing_cycle, 
        amount, description, create_time, update_time, latest_join_time, 
        service_expire_time, review_picture, third_detail, reason, 
        check_create_time, check_time, check_user, check_count, is_new_product
    </sql>

    <!-- 根据产品ID和状态查询家庭组列表 -->
    <select id="selectByProductIdAndStatus" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM fg_substore
        WHERE product_id = #{productId}
        <if test="status != null">
            AND family_group_status = #{status}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 根据创建用户查询家庭组列表 -->
    <select id="selectByCreateUser" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM fg_substore
        WHERE create_user = #{createUser}
        ORDER BY create_time DESC
    </select>

    <!-- 根据预订用户查询家庭组列表 -->
    <select id="selectByBookingUser" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM fg_substore
        WHERE booking_user_id = #{bookingUserId}
        ORDER BY create_time DESC
    </select>

    <!-- 查询有空位的家庭组 -->
    <select id="selectAvailableGroups" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM fg_substore
        WHERE sum_vacancy > 0
        AND family_group_status = #{status}
        <if test="productId != null">
            AND product_id = #{productId}
        </if>
        <if test="categoryId != null and categoryId != ''">
            AND category_id = #{categoryId}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 根据套餐ID查询家庭组 -->
    <select id="selectByPlanId" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM fg_substore
        WHERE plan_id = #{planId}
        ORDER BY create_time DESC
    </select>

    <!-- 查询即将过期的家庭组 -->
    <select id="selectExpiringSoon" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM fg_substore
        WHERE service_expire_time IS NOT NULL
        AND service_expire_time BETWEEN #{startTime} AND #{endTime}
        ORDER BY service_expire_time ASC
    </select>

    <!-- 统计家庭组数量 -->
    <select id="countByStatus" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM fg_substore
        WHERE family_group_status = #{status}
        <if test="productId != null">
            AND product_id = #{productId}
        </if>
    </select>

    <!-- 更新家庭组状态 -->
    <update id="updateStatus">
        UPDATE fg_substore
        SET family_group_status = #{status},
            update_time = #{updateTime}
        WHERE family_group_id = #{familyGroupId}
    </update>

    <!-- 更新空位数量 -->
    <update id="updateVacancy">
        UPDATE fg_substore
        SET sum_vacancy = #{sumVacancy},
            actual_join_count = #{actualJoinCount},
            update_time = #{updateTime}
        WHERE family_group_id = #{familyGroupId}
    </update>

    <!-- 批量更新家庭组状态 -->
    <update id="batchUpdateStatus">
        UPDATE fg_substore
        SET family_group_status = #{status},
            update_time = #{updateTime}
        WHERE family_group_id IN
        <foreach collection="familyGroupIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>
